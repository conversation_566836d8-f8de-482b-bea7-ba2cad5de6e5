import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { useMemo, useState } from 'react'
// import { DatePicker } from '@/components/date-picker'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Command, CommandEmpty, CommandGroup, CommandInput } from '@/components/ui/command'
import { CommandItem } from '@/components/ui/command'
import { Badge } from '@/components/ui/badge'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { getCountryList, getMasterApi } from '../api'

interface DataTableToolbarProps<TData> {
  readonly table: Table<TData>
  readonly onFilterChanged?: any
}

const domainTypeOptions = [
  'discreetdating.club',
  'passionhub.net',
  'lovequest.org',
  'secretlovers.co',
  'adultmatch.com',
  'flirtzone.com'
]

const buyerStatusOptions = [
  'Buyer',
  'Non Buyer'
]

const userStatusOptions = [
  'Active',
  'Deleted',
  'Incomplete'
]

const affiliateStatusOptions = [
  'Affiliate',
  'Non-affiliate'
]

const onlineStatusOptions = [
  'Online',
  'Offline'
]

const pictureStatusOptions = [
  'Approved',
  'Non Approved'
]

export function DataTableToolbar<TData>({
  table,
  onFilterChanged
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0
  const { data: masterData } = getMasterApi()
  const { data: countryList } = getCountryList()
  const [countries, setCountries] = useState<any>(countryList?.country || [])

  const masterOptions = useMemo(() => {
    const master = masterData?.master || {};
    return {
      model_group: master.model_group || [],
    };
  }, [masterData]);

  // Use model_group from masterOptions as groupOptions
  const groupOptions = masterOptions.model_group || [];

  const [filters, setFilters] = useState<{
    domainType: string;
    country: string;
    city: string;
    cityInput: string;
    buyerStatus?: string;
    userStatus: string;
    affiliateStatus?: string;
    onlineStatus?: string;
    pictureStatus?: string;
  }>({
    domainType: '',
    country: '',
    city: '',
    cityInput: '',
    buyerStatus: '',
    userStatus: '',
    affiliateStatus: '',
    onlineStatus: '',
    pictureStatus: '',
  })

  const [hasSearched, setHasSearched] = useState(false)
  const [cityDropdownOpen, setCityDropdownOpen] = useState(false)
  const [initialFilters, setInitialFilters] = useState<{
    domainType: string;
    country: string;
    city: string;
    cityInput: string;
    buyerStatus?: string;
    userStatus: string;
    affiliateStatus?: string;
    onlineStatus?: string;
    pictureStatus?: string;
  }>({
    domainType: '',
    country: '',
    city: '',
    cityInput: '',
    buyerStatus: '',
    userStatus: '',
    affiliateStatus: '',
    onlineStatus: '',
    pictureStatus: '',
  })

  const handleFilterChange = (
    key: 'domainType' | 'country' | 'city' | 'cityInput' | 'buyerStatus' | 'userStatus' | 'affiliateStatus' | 'onlineStatus' | 'pictureStatus',
    value: string | string[] | number[]
  ) => {
    if (key === 'buyerStatus' && (value === undefined || value === null)) {
      value = '';
    }
    // For multi-selects, convert array to comma-separated string
    if (["domainType", "country", "city", "userStatus"].includes(key)) {
      if (Array.isArray(value)) {
        value = value.filter((v) => v !== '' && v !== undefined && v !== null).join(',');
      }
    }
    setFilters((prev) => {
      const updated = { ...prev, [key]: value };
      console.log('Filters:', updated);
      return updated;
    });
  }

  const handleSearch = () => {
    // Apply filters to the table
    if (filters.domainType && filters.domainType.length > 0) {
      const domainTypeColumn = table.getColumn('domainType')
      if (domainTypeColumn) {
        domainTypeColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string) => {
          return filterValue.split(',').includes(row.original.domainType)
        }
        domainTypeColumn.setFilterValue(filters.domainType)
      }
    }
    if (filters.country && filters.country.length > 0) {
      const countryColumn = table.getColumn('country')
      if (countryColumn) {
        countryColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string) => {
          return filterValue.split(',').map(Number).includes(row.original.country)
        }
        countryColumn.setFilterValue(filters.country)
      }
    }
    if (filters.city && filters.city.length > 0) {
      const cityColumn = table.getColumn('city')
      if (cityColumn) {
        cityColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string) => {
          return filterValue.split(',').includes(row.original.city)
        }
        cityColumn.setFilterValue(filters.city)
      }
    }
    if (filters.buyerStatus) {
      const buyerStatusColumn = table.getColumn('buyerStatus')
      if (buyerStatusColumn) {
        buyerStatusColumn.setFilterValue(filters.buyerStatus)
      }
    }
    if (filters.userStatus && filters.userStatus.length > 0) {
      const userStatusColumn = table.getColumn('userStatus')
      if (userStatusColumn) {
        userStatusColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string) => {
          return filterValue.split(',').includes(row.original.userStatus)
        }
        userStatusColumn.setFilterValue(filters.userStatus)
      }
    }
    if (filters.affiliateStatus) {
      const affiliateStatusColumn = table.getColumn('affiliateStatus')
      if (affiliateStatusColumn) {
        affiliateStatusColumn.setFilterValue(filters.affiliateStatus)
      }
    }
    if (filters.onlineStatus) {
      const onlineStatusColumn = table.getColumn('onlineStatus')
      if (onlineStatusColumn) {
        onlineStatusColumn.setFilterValue(filters.onlineStatus)
      }
    }
    if (filters.pictureStatus) {
      const pictureStatusColumn = table.getColumn('pictureStatus')
      if (pictureStatusColumn) {
        pictureStatusColumn.setFilterValue(filters.pictureStatus)
      }
    }
    setHasSearched(true)
    setInitialFilters({ ...filters })
    onFilterChanged(filters, 1)
  }

  const handleReset = () => {
    table.resetColumnFilters()
    const f: any = {
      domainType: '',
      country: '',
      city: '',
      cityInput: '',
      buyerStatus: '',
      userStatus: '',
      affiliateStatus: '',
      onlineStatus: '',
      pictureStatus: '',
    }
    setFilters(f)
    setInitialFilters(f)
    setHasSearched(false)
    onFilterChanged(f, 0)
  }

  const hasFilterChanges = Boolean(
    filters.domainType !== initialFilters.domainType ||
    filters.country !== initialFilters.country ||
    filters.city !== initialFilters.city ||
    filters.buyerStatus !== initialFilters.buyerStatus ||
    filters.userStatus !== initialFilters.userStatus ||
    filters.affiliateStatus !== initialFilters.affiliateStatus ||
    filters.onlineStatus !== initialFilters.onlineStatus ||
    filters.pictureStatus !== initialFilters.pictureStatus
  )

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center gap-4 flex-wrap'>
        {/* Multi-select popover for domainType */}
        <Popover>
          <PopoverTrigger asChild>
            <div style={{ background: "#fff" }} className={cn(
              "flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input px-3 py-2 text-sm shadow-sm cursor-pointer",
              !filters.domainType.length && "text-muted-foreground"
            )}>
              {filters.domainType.length === 0 && <span>Select domain type</span>}
              {filters.domainType.split(',').filter(Boolean).slice(0, 2).map((val: string) => (
                <div className="flex gap-1 items-center bg-[#f5f5f5] rounded p-1" key={val}>
                  <Badge className="cursor-default border-0 p-0 bg-transparent text-black">
                    {val}
                  </Badge>
                  <X className="h-3 w-3 cursor-pointer" onClick={e => {
                    e.stopPropagation();
                    const arr = filters.domainType.split(',').filter(Boolean).filter((v: string) => v !== val)
                    handleFilterChange('domainType', arr)
                  }} />
                </div>
              ))}
              {filters.domainType.split(',').filter(Boolean).length > 2 && (
                <span className="ml-2 text-muted-foreground">...</span>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder="Search domain type..." />
              <CommandEmpty>No domain type found.</CommandEmpty>
              <CommandGroup>
                {(() => {
                  const selectedArr = filters.domainType.split(',').filter(Boolean)
                  const sortedDomainTypes = [
                    ...domainTypeOptions.filter((type) => selectedArr.includes(type)),
                    ...domainTypeOptions.filter((type) => !selectedArr.includes(type)),
                  ];
                  return sortedDomainTypes.map((type) => (
                    <CommandItem
                      key={type}
                      onSelect={() => {
                        const arr = filters.domainType.split(',').filter(Boolean)
                        const selected = arr.includes(type)
                          ? arr.filter((v: string) => v !== type)
                          : [...arr, type]
                        handleFilterChange('domainType', selected)
                      }}
                      className="cursor-pointer"
                    >
                      <span>{type}</span>
                      {selectedArr.includes(type) && (
                        <span className="ml-auto text-primary">✓</span>
                      )}
                    </CommandItem>
                  ));
                })()}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>
        {/* Multi-select popover for country */}
        <Popover>
          <PopoverTrigger asChild>
            <div style={{ background: "#fff" }} className={cn(
              "flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input px-3 py-2 text-sm shadow-sm cursor-pointer",
              !filters.country.length && "text-muted-foreground"
            )}>
              {filters.country.length === 0 && <span>Select country</span>}
              {filters.country.split(',').filter(Boolean).slice(0, 2).map((idStr: string) => {
                const id = Number(idStr)
                const country = countries.find((c: any) => c.id === id);
                return (
                  <div className="flex gap-1 items-center bg-[#f5f5f5] rounded p-1" key={id}>
                    <Badge className="cursor-default border-0 p-0 bg-transparent text-black">
                      {country?.name ?? id}
                    </Badge>
                    <X className="h-3 w-3 cursor-pointer" onClick={e => {
                      e.stopPropagation();
                      const arr = filters.country.split(',').filter(Boolean).filter((v: string) => v !== idStr)
                      handleFilterChange('country', arr)
                    }} />
                  </div>
                )
              })}
              {filters.country.split(',').filter(Boolean).length > 2 && (
                <span className="ml-2 text-muted-foreground">...</span>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder="Search country..." />
              <CommandEmpty>No country found.</CommandEmpty>
              <CommandGroup>
                {(() => {
                  const selectedArr = filters.country.split(',').filter(Boolean)
                  const sortedCountries = [
                    ...countries.filter((c: any) => selectedArr.includes(String(c.id))),
                    ...countries.filter((c: any) => !selectedArr.includes(String(c.id))),
                  ];
                  return sortedCountries.map((country: any) => (
                    <CommandItem
                      key={country.id}
                      onSelect={() => {
                        const arr = filters.country.split(',').filter(Boolean)
                        const selected = arr.includes(String(country.id))
                          ? arr.filter((v: string) => v !== String(country.id))
                          : [...arr, String(country.id)]
                        handleFilterChange('country', selected)
                      }}
                      className="cursor-pointer"
                    >
                      <span>{country.name}</span>
                      {selectedArr.includes(String(country.id)) && (
                        <span className="ml-auto text-primary">✓</span>
                      )}
                    </CommandItem>
                  ));
                })()}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>
        {/* Multi-input for cities with dropdown for selected */}
        <div className="relative w-[200px]">
          <input
            type="text"
            style={{ background: '#fff' }}
            className="w-full border border-input rounded-md px-3 py-2 text-sm shadow-sm"
            placeholder={filters.city.split(',').filter(Boolean).length === 0 ? "Type and press Enter to add cities" : "Add city..."}
            value={filters.cityInput || ""}
            onFocus={() => setCityDropdownOpen(true)}
            onBlur={() => setTimeout(() => setCityDropdownOpen(false), 150)}
            onChange={e => handleFilterChange('cityInput', e.target.value)}
            onKeyDown={e => {
              if (
                (e.key === 'Enter' || e.key === ',') &&
                filters.cityInput &&
                !filters.city.split(',').filter(Boolean).includes(filters.cityInput.trim())
              ) {
                const arr = [...filters.city.split(',').filter(Boolean), filters.cityInput.trim()]
                handleFilterChange('city', arr);
                handleFilterChange('cityInput', '');
                e.preventDefault();
              }
              if (e.key === 'Backspace' && !filters.cityInput && filters.city.split(',').filter(Boolean).length > 0) {
                // Remove last city on backspace if input is empty
                const arr = filters.city.split(',').filter(Boolean).slice(0, -1)
                handleFilterChange('city', arr);
              }
            }}
          />
          {cityDropdownOpen && filters.city.split(',').filter(Boolean).length > 0 && (
            <div className="absolute left-0 right-0 mt-1 bg-white border border-input rounded-md shadow-lg z-10 p-2 flex flex-wrap gap-1">
              {filters.city.split(',').filter(Boolean).map((city: string) => (
                <div className="flex gap-1 items-center bg-[#f5f5f5] rounded p-1" key={city}>
                  <Badge className="cursor-default border-0 p-0 bg-transparent text-black">
                    {city}
                  </Badge>
                  <X className="h-3 w-3 cursor-pointer"
                    onMouseDown={e => e.preventDefault()}
                    onClick={() => {
                      const arr = filters.city.split(',').filter(Boolean).filter((v: string) => v !== city)
                      handleFilterChange('city', arr)
                    }} />
                </div>
              ))}
            </div>
          )}
        </div>
        {/* Buyer Status single-select dropdown */}
        <FilterSelect
          value={filters.buyerStatus || ''}
          placeholder="Select Buyer Status"
          options={buyerStatusOptions}
          onChange={(value) => handleFilterChange('buyerStatus', value || '')}
        />
        {/* User Status multi-select popover */}
        <Popover>
          <PopoverTrigger asChild>
            <div style={{ background: "#fff" }} className={cn(
              "flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input px-3 py-2 text-sm shadow-sm cursor-pointer",
              !filters.userStatus.length && "text-muted-foreground"
            )}>
              {filters.userStatus.length === 0 && <span>Select user status</span>}
              {filters.userStatus.split(',').filter(Boolean).slice(0, 2).map((val: string) => (
                <div className="flex gap-1 items-center bg-[#f5f5f5] rounded p-1" key={val}>
                  <Badge className="cursor-default border-0 p-0 bg-transparent text-black">
                    {val}
                  </Badge>
                  <X className="h-3 w-3 cursor-pointer" onClick={e => {
                    e.stopPropagation();
                    const arr = filters.userStatus.split(',').filter(Boolean).filter((v: string) => v !== val)
                    handleFilterChange('userStatus', arr)
                  }} />
                </div>
              ))}
              {filters.userStatus.split(',').filter(Boolean).length > 2 && (
                <span className="ml-2 text-muted-foreground">...</span>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder="Search user status..." />
              <CommandEmpty>No user status found.</CommandEmpty>
              <CommandGroup>
                {(() => {
                  const selectedArr = filters.userStatus.split(',').filter(Boolean)
                  const sortedUserStatus = [
                    ...userStatusOptions.filter((status) => selectedArr.includes(status)),
                    ...userStatusOptions.filter((status) => !selectedArr.includes(status)),
                  ];
                  return sortedUserStatus.map((status) => (
                    <CommandItem
                      key={status}
                      onSelect={() => {
                        const arr = filters.userStatus.split(',').filter(Boolean)
                        const selected = arr.includes(status)
                          ? arr.filter((v: string) => v !== status)
                          : [...arr, status]
                        handleFilterChange('userStatus', selected)
                      }}
                      className="cursor-pointer"
                    >
                      <span>{status}</span>
                      {selectedArr.includes(status) && (
                        <span className="ml-auto text-primary">✓</span>
                      )}
                    </CommandItem>
                  ));
                })()}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>
        {/* Affiliate Status single-select dropdown */}
        <FilterSelect
          value={filters.affiliateStatus || ''}
          placeholder="Select Affiliate Status"
          options={affiliateStatusOptions}
          onChange={(value) => handleFilterChange('affiliateStatus', value || '')}
        />
        {/* Online Status single-select dropdown */}
        <FilterSelect
          value={filters.onlineStatus || ''}
          placeholder="Select Online Status"
          options={onlineStatusOptions}
          onChange={(value) => handleFilterChange('onlineStatus', value || '')}
        />
        {/* Picture Status single-select dropdown */}
        <FilterSelect
          value={filters.pictureStatus || ''}
          placeholder="Select Picture Status"
          options={pictureStatusOptions}
          onChange={(value) => handleFilterChange('pictureStatus', value || '')}
        />
        {/* DatePicker components removed */}
        <Button
          onClick={handleSearch}
          className="h-8 px-3"
          disabled={!hasFilterChanges}
        >
          Search
        </Button>
        {(isFiltered || hasSearched) && (
          <Button
            variant='outline'
            onClick={handleReset}
            className='h-8 px-2 lg:px-3'
          >
            Reset
            {/* <Cross2Icon className='ml-2 h-4 w-4' /> */}
          </Button>
        )}
      </div>
    </div>
  )
}
