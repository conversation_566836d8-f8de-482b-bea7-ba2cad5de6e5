import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";


export const getFlirtMessageApi = (params = {}) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.FLIRT_MESSAGES, {
                params,
            });
            return response?.data ?? {}; // return [] or {} as a fallback
        },
        queryKey: ["flirt-message-list"],
    });


export const addFlirtMessageApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.post(API_ENDPOINTS.FLIRT_MESSAGES, payload);
        },
    });


export const updateFlirtMessageApi = () =>
    useMutation({
        mutationFn: async ({ id, ...payload }: any) => {
            return await apiClient.put(`${API_ENDPOINTS.FLIRT_MESSAGES}/${id}`, payload);
        },
    });

export const getFlirtMessageDetails = (id: any = {}) =>
    useQuery({
        queryFn: async () => {
            if (typeof id === 'string') {
                const response = await apiClient.get(`${API_ENDPOINTS.FLIRT_MESSAGES}/${id}`)
                return response?.data ?? {}; // return [] or {} as a fallback
            }
            return {}

        },
        queryKey: ["flirt-message-details", id],
        enabled: !!id
    });

export const deleteFlirtMessageApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.delete(`${API_ENDPOINTS.FLIRT_MESSAGES}/${payload?.id}`);
        },
    });

export const uploadFlirtMessageCsvApi = () =>
    useMutation({
        mutationFn: async (fileOrFormData: File | FormData) => {
            let formData: FormData;
            if (fileOrFormData instanceof FormData) {
                formData = fileOrFormData;
            } else {
                formData = new FormData();
                formData.append("file", fileOrFormData);
            }
            return await apiClient.post(API_ENDPOINTS.FLIRT_UPLOAD_CSV, formData, {
                headers: {
                    "Content-Type": "multipart/form-data",
                },
            });
        },
    });

