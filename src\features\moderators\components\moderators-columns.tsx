import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import LongText from '@/components/long-text'
import { Moderator } from '../data/schema'
import { DataTableColumnHeader } from './data-table-column-header'
import { DataTableRowActions } from './data-table-row-actions'
import { formatTimestampToLocal, toTitleCase } from '@/features/members/utils/utilities'
import { Badge } from '@/components/ui/badge'

export const columns: ColumnDef<Moderator>[] = [
  {
    accessorKey: 'serialNumber',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='#' />
    ),
    cell: ({ row }) => (
      <div>{row.getValue('serialNumber')}</div>
    ),
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Name' />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-36'>{row.getValue('name')}</LongText>
    ),
    meta: {
      className: cn(
        'drop-shadow-[0_1px_2px_rgb(0_0_0_/_0.1)] dark:drop-shadow-[0_1px_2px_rgb(255_255_255_/_0.1)] lg:drop-shadow-none',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'sticky left-0 md:table-cell'
      ),
    },
    enableHiding: false,
  },
  {
    id: 'moderatorType',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Moderator Type' />
    ),
    cell: ({ row }) => {
      return <LongText className='max-w-36'>{row.original.moderatorType}</LongText>
    },
    meta: { className: 'w-36' },
  },
  // {
  //   accessorKey: 'email',
  //   header: ({ column }) => (
  //     <DataTableColumnHeader column={column} title='Email' />
  //   ),
  //   cell: ({ row }) => (
  //     <div className='w-fit text-nowrap'>{row.getValue('email')}</div>
  //   ),
  // },
  {
    accessorKey: 'phone',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Phone Number' />
    ),
    cell: ({ row }) => <div>{row.getValue('phone')}</div>,
    enableSorting: false,
  },
  {
    accessorKey: 'nickname',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Nick Name' />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-36'>{row.getValue('nickname')}</LongText>
    ),
    meta: {
      className: cn(
        'drop-shadow-[0_1px_2px_rgb(0_0_0_/_0.1)] dark:drop-shadow-[0_1px_2px_rgb(255_255_255_/_0.1)] lg:drop-shadow-none',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'sticky left-6 md:table-cell'
      ),
    },
    enableHiding: false,
  },
  {
    accessorKey: 'lastMessageAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Last Message At' />
    ),
    cell: ({ row }: any) => {
      const date = row.original.lastMessageAt
      return <div>{formatTimestampToLocal(date)}</div>
    },

    meta: {
      className: cn(
        'drop-shadow-[0_1px_2px_rgb(0_0_0_/_0.1)] dark:drop-shadow-[0_1px_2px_rgb(255_255_255_/_0.1)] lg:drop-shadow-none',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'sticky left-6 md:table-cell'
      ),
    },
    enableHiding: false,
  },
  {
    accessorKey: 'isSuspended',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Status' />
    ),
    cell: ({ row }) => {
      const isSuspended = row.getValue('isSuspended') as boolean
      return (
        <div className='flex space-x-2'>
          <Badge variant={!isSuspended ? 'default' : 'secondary'}>
            {!isSuspended ? 'Active' : 'Inactive'}
          </Badge>
        </div>
      )
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Created At' />
    ),
    cell: ({ row }) => {
      const date = row.original.createdAt
      return <div>{new Date(date).toLocaleDateString()}</div>
    },

    meta: {
      className: cn(
        'drop-shadow-[0_1px_2px_rgb(0_0_0_/_0.1)] dark:drop-shadow-[0_1px_2px_rgb(255_255_255_/_0.1)] lg:drop-shadow-none',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'sticky left-6 md:table-cell'
      ),
    },
    enableHiding: false,
  },

  {
    accessorKey: 'actions',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Actions' />
    ),
    enableSorting: false,
    id: 'actions',
    cell: DataTableRowActions,
  },
]
