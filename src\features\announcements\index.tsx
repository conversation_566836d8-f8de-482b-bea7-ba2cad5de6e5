import { AnnouncementPrimaryButtons } from './components/announcement-primary-buttons';
import { Header } from '@/components/layout/header';
import { ThemeSwitch } from '@/components/theme-switch';
import { ProfileDropdown } from '@/components/profile-dropdown';
import { AnnouncementActionDialog } from './components/announcement-add-action';
import { Link, useLocation } from '@tanstack/react-router';
import { useState, ReactNode } from 'react';

interface AnnouncementsTabbedViewProps {
    children?: ReactNode;
}

export default function AnnouncementsTabbedView({ children }: AnnouncementsTabbedViewProps) {
    const [open, setOpen] = useState(false);
    const location = useLocation();

    return (
        <>
            <Header fixed>
                <div className='ml-auto flex items-center space-x-4'>
                    <ThemeSwitch />
                    <ProfileDropdown />
                </div>
            </Header>
            <div className="p-6 pt-4" style={{ marginTop: '60px' }} >

                <div className="flex border-b mb-4 justify-between lg:flex-row flex-col gap-3">
                    <div>
                        <Link
                            to="/announcements/white-labels"
                            className={
                                `px-4 py-2 ${location.pathname.includes('/announcements/white-labels') ? 'border-b-2 border-black font-semibold' : 'text-gray-500'
                                }`
                            }
                        >
                            White Label
                        </Link>
                        <Link
                            to="/announcements/moderators"
                            className={`px-4 py-2 ml-4 ${location.pathname.includes('/announcements/moderators') ? 'border-b-2 border-black font-semibold' : 'text-gray-500'
                                }`}
                        >
                            Moderators
                        </Link>
                        <Link
                            to="/announcements/affiliates"
                            className={`px-4 py-2 ml-4 ${location.pathname.includes('/announcements/affiliates') ? 'border-b-2 border-black font-semibold' : 'text-gray-500'
                                }`}
                        >
                            Affiliates
                        </Link>
                    </div>
                    <div>
                        <AnnouncementPrimaryButtons openModal={() => { setOpen(true) }} />
                    </div>
                </div>

                {/* Route content will be rendered here */}
                <div>
                    {children}
                </div>
            </div>

            <AnnouncementActionDialog open={open} onOpenChange={setOpen} />
        </>
    );
}