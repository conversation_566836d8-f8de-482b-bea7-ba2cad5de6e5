import { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>og,
  AlertDialog<PERSON>ontent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogFooter,
  AlertDialogCancel,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { CalendarIcon } from "@radix-ui/react-icons";
import { IconCalendar } from "@tabler/icons-react";

const HOLD_OPTIONS = [
  { label: "5 min", value: 5 },
  { label: "15 min", value: 15 },
  { label: "30 min", value: 30 },
  { label: "01 Hr", value: 60 },
  { label: "02 Hr", value: 120 },
  { label: "05 Hr", value: 300 },
  { label: "01 day", value: 1440 },
  { label: "02 day", value: 2880 },
  { label: "03 day", value: 4320 },
  { label: "04 day", value: 5760 },
];

interface HoldMessageModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (data: { selected: number | null; reason: string; date: Date | null }) => void;
  isLoading?: boolean;
}

export function HoldMessageModal({ open, onOpenChange, onSave, isLoading }: HoldMessageModalProps) {
  const [selected, setSelected] = useState<number | null>(null);
  const [reason, setReason] = useState("");
  const [date, setDate] = useState<Date | null>(null);

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent width="max-w-xl" className="p-0 rounded-xl bg-white">
        <AlertDialogHeader className="px-6 pt-6 pb-2 text-left">
          <AlertDialogTitle className="text-2xl font-semibold">Hold Message</AlertDialogTitle>
        </AlertDialogHeader>
        <div className="px-6">
          <div className="font-medium mb-2">Hold Time</div>
          <div className="relative mb-3">
            <input
              type="text"
              placeholder="Select your time"
              className="w-full border rounded-lg px-4 py-3 text-base pr-10 outline-none"
              readOnly
              value={date ? date.toLocaleString() : ""}
              onClick={() => {/* open date picker */ }}
            />
            <IconCalendar className="absolute right-3 top-3.5 text-[#171717]" size={20} />
          </div>
          <div className="grid grid-cols-4 gap-2 mb-6">
            {HOLD_OPTIONS.map((opt) => (
              <button
                key={opt.value}
                type="button"
                className={cn(
                  "border rounded-full px-4 py-2 text-base",
                  selected === opt.value
                    ? "bg-[#171717] text-white border-[#171717]"
                    : "bg-white text-gray-700 border-gray-300 hover:bg-gray-100"
                )}
                onClick={() => setSelected(opt.value)}
              >
                {opt.label}
              </button>
            ))}
          </div>
          <textarea
            className="w-full border rounded-lg px-3 py-2 text-base"
            rows={4}
            placeholder="Type your reason here..."
            value={reason}
            onChange={(e) => setReason(e.target.value)}
          />
        </div>
        <AlertDialogFooter className="px-6 pb-6 pt-2 flex-row-reverse gap-2">
          <AlertDialogCancel className="min-w-[120px] border-gray-300 bg-transparent cursor-pointer">Cancel</AlertDialogCancel>
          <Button
            onClick={() => onSave({ selected, reason, date })}
            disabled={isLoading || !selected || !reason}
            className="min-w-[120px] bg-[#171717] cursor-pointer"
          >
            Save
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
