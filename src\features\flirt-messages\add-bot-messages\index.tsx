import { Main } from "@/components/layout/main";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { addFlirtMessageApi, getFlirtMessageDetails, updateFlirtMessageApi } from "../api";
import { useNavigate, useParams } from "@tanstack/react-router";
import { useEffect } from "react";
import { toast } from "sonner";
import { END_POINTS } from "@/features/members/utils/constant";

const FlirtMessageSchema = z.object({
    en: z.string().min(1, "Required"),
    fr: z.string().min(1, "Required"),
    de: z.string().min(1, "Required"),
    nl: z.string().min(1, "Required"),
    da: z.string().min(1, "Required"),
    fi: z.string().min(1, "Required"),
    it: z.string().min(1, "Required"),
    no: z.string().min(1, "Required"),
    pl: z.string().min(1, "Required"),
    pt: z.string().min(1, "Required"),
    el: z.string().min(1, "Required"),
    es: z.string().min(1, "Required"),
    sv: z.string().min(1, "Required"),
});

type FlirtMessageFormValues = z.infer<typeof FlirtMessageSchema>;

export default function AddFlirtMessage() {
    const navigate = useNavigate()
    const { mutateAsync: addFlirtMessageMutation } = addFlirtMessageApi();
    const { mutateAsync: updateFlirtMessageMutation } = updateFlirtMessageApi()
    const { msgId } = useParams({ strict: false });
    const { data = {} } = getFlirtMessageDetails(msgId);

    const form = useForm<FlirtMessageFormValues>({
        resolver: zodResolver(FlirtMessageSchema),
        defaultValues: {
            en: "",
            fr: "",
            de: "",
            nl: "",
            da: "",
            fi: "",
            it: "",
            no: "",
            pl: "",
            pt: "",
            el: "",
            es: "",
            sv: "",
        },
    });

    // Prefill form when data.items is available
    useEffect(() => {
        if (data.items) {
            const itemsObject = (data.items || []).reduce((acc: any, item: any) => {
                acc[item.languageCode] = item.message;
                return acc;
            }, {});
            form.reset({
                ...form.getValues(),
                ...itemsObject
            });
        }
    }, [data, form]);

    const { control, handleSubmit } = form;

    const onSubmit = async (values: FlirtMessageFormValues) => {
        const items = [
            { languageCode: "en", message: values.en },
            { languageCode: "fr", message: values.fr },
            { languageCode: "de", message: values.de },
            { languageCode: "nl", message: values.nl },
            { languageCode: "da", message: values.da },
            { languageCode: "fi", message: values.fi },
            { languageCode: "it", message: values.it },
            { languageCode: "no", message: values.no },
            { languageCode: "pl", message: values.pl },
            { languageCode: "pt", message: values.pt },
            { languageCode: "el", message: values.el },
            { languageCode: "es", message: values.es },
            { languageCode: "sv", message: values.sv },
        ];

        const payload = {
            items
        };

        if (typeof msgId === "string") {
            const response: any = await updateFlirtMessageMutation({
                ...payload,
                id: data?.id
            })
            if (response?.success) {
                toast.success("Flirt Message has been updated!")
            }
        } else {

            const response: any = await addFlirtMessageMutation(payload);
            if (response?.success) {
                navigate({ to: END_POINTS.FLIRT_MESSAGES });
            }
        };
    }

    return (
        <Main>
            <div className="space-y-0.5">
                <h1 className="text-2xl font-bold tracking-tight md:text-3xl">
                    Create Flirt Message
                </h1>
                <p className="text-muted-foreground">Creating a new flirt message.</p>
            </div>
            <Separator className="my-4 lg:my-3" />
            <div className="flex flex-1 flex-col space-y-2 overflow-hidden md:space-y-2 xl:flex-row lg:space-y-0 lg:space-x-12">
                <div className="flex w-full overflow-y-hidden p-1">
                    <Form {...form}>
                        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Flirt message Details</CardTitle>
                                </CardHeader>
                                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                                    <FormField name="en" control={control} render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>English</FormLabel>
                                            <FormControl><Input {...field} /></FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                    <FormField name="fr" control={control} render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>French</FormLabel>
                                            <FormControl><Input {...field} /></FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                    <FormField name="de" control={control} render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>German</FormLabel>
                                            <FormControl><Input {...field} /></FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                    <FormField name="nl" control={control} render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Dutch</FormLabel>
                                            <FormControl><Input {...field} /></FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                    <FormField name="da" control={control} render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Danish</FormLabel>
                                            <FormControl><Input {...field} /></FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                    <FormField name="fi" control={control} render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Finnish</FormLabel>
                                            <FormControl><Input {...field} /></FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                    <FormField name="it" control={control} render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Italian</FormLabel>
                                            <FormControl><Input {...field} /></FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                    <FormField name="no" control={control} render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Norwegian</FormLabel>
                                            <FormControl><Input {...field} /></FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                    <FormField name="pl" control={control} render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Polish</FormLabel>
                                            <FormControl><Input {...field} /></FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                    <FormField name="pt" control={control} render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Portuguese</FormLabel>
                                            <FormControl><Input {...field} /></FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                    <FormField name="el" control={control} render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Greek</FormLabel>
                                            <FormControl><Input {...field} /></FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                    <FormField name="es" control={control} render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Spanish</FormLabel>
                                            <FormControl><Input {...field} /></FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                    <FormField name="sv" control={control} render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Swedish</FormLabel>
                                            <FormControl><Input {...field} /></FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                </CardContent>
                            </Card>

                            <div className="md:col-span-2 flex mt-4 justify-end">
                                <Button type="submit">
                                    {typeof msgId === "string" ? "Update" : "Save"}
                                </Button>
                            </div>
                        </form>
                    </Form>
                </div>
            </div>
        </Main>
    );
} 