import * as React from 'react'
import { DayPicker } from 'react-day-picker'
import { cn } from '@/lib/utils'
import { buttonVariants } from '@/components/ui/button'


function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: React.ComponentProps<typeof DayPicker>) {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn(
        'p-4 bg-white rounded-xl shadow-lg border border-gray-200 max-w-xs',
        className
      )}
      mode='single'
      classNames={{
        months: 'flex flex-col gap-2',
        month: 'flex flex-col gap-4',
        month_caption: 'flex flex-row gap-2 justify-center items-center pt-1 relative w-full',
        caption_label: 'hidden',
        dropdowns: 'flex flex-row gap-2 justify-center items-center w-full',
        nav: 'hidden',
        button_previous: 'hidden',
        button_next: 'hidden',
        month_grid: 'w-full border-collapse space-x-1',
        weekdays: 'flex gap-1',
        weekday:
          'text-muted-foreground rounded-md w-8 h-8 flex items-center justify-center font-normal text-xs text-gray-500',
        week: 'flex w-full mt-2 gap-1',
        day: cn(
          'relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md',
          props.mode === 'range'
            ? '[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md'
            : 'aria-selected:rounded-md [&[aria-selected="true"]>button]:hover:bg-foreground [&[aria-selected="true"]>button]:hover:text-background/85'
        ),
        day_button: cn(
          buttonVariants({ variant: 'ghost' }),
          'size-8 p-0 font-normal aria-selected:opacity-100 rounded-full transition-colors duration-150'
        ),
        day_selected: 'opacity-100 bg-yellow-500 text-white',
        range_start:
          'day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground',
        range_end:
          'day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground',
        selected:
          'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',
        today: 'bg-accent text-accent-foreground rounded-md border border-primary',
        outside:
          'day-outside text-muted-foreground aria-selected:text-muted-foreground',
        disabled: 'text-muted-foreground opacity-50',
        range_middle:
          'aria-selected:bg-accent aria-selected:text-accent-foreground',
        hidden: 'invisible',
        dropdown: 'rounded-md border border-gray-300 bg-white px-2 py-1 text-sm shadow-sm focus:outline-none max-h-[150px] overflow-y-auto',
        ...classNames,
      }}
      {...props}
    />
  )
}

export { Calendar }
