import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { useRef, useState } from 'react'
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover'
import { Command, CommandGroup, CommandItem, CommandInput, CommandEmpty } from '@/components/ui/command'
import { Badge } from '@/components/ui/badge'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { getCountryList } from '@/features/members/api'
interface DataTableToolbarProps<TData> {
  readonly table: Table<TData>
  readonly onFilterChanged?: any
}

const moderatorTypeOptions = [
  'Default Moderator',
  'WL Moderator'
]

const statusOptions = [
  'ACTIVE',
  'INACTIVE',
  'SUSPENDED',
  'PENDING',
  'BLOCKED'
]

export function DataTableToolbar<TData>({
  table,
  onFilterChanged
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0
  const { data: countryList } = getCountryList()
  const [countries, setCountries] = useState<any>(countryList?.country || [])
  const [filters, setFilters] = useState<{
    search: string;
    moderatorType: string;
    status: string;
    country: string;
    city: string;
    cityInput: string;
  }>({
    search: '',
    moderatorType: '',
    status: '',
    country: '',
    city: '',
    cityInput: '', // for tag input
  })

  const [hasSearched, setHasSearched] = useState(false)
  const [cityDropdownOpen, setCityDropdownOpen] = useState(false);
  const cityInputRef = useRef<HTMLInputElement>(null);

  const handleFilterChange = (
    key: 'search' | 'moderatorType' | 'status' | 'country' | 'city' | 'cityInput',
    value: string | number[] | string[]
  ) => {
    let newValue = value;
    // For multi-selects, always store as comma-separated string
    if ([
      'moderatorType', 'status', 'country', 'city'
    ].includes(key)) {
      if (Array.isArray(value)) {
        newValue = value.join(',');
      }
    }
    setFilters((prev) => ({ ...prev, [key]: newValue }));
    console.log({ ...filters, [key]: newValue });
  };

  const handleSearch = () => {
    // Apply filters to the table
    if (filters.search) {
      table.getColumn('name')?.setFilterValue(filters.search)
    }
    if (filters.moderatorType) {
      const selected = filters.moderatorType.split(',').filter(Boolean);
      const moderatorTypeColumn = table.getColumn('moderatorType')
      if (moderatorTypeColumn) {
        moderatorTypeColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string[]) => {
          return filterValue.includes(row.original.moderatorType)
        }
        moderatorTypeColumn.setFilterValue(selected)
      }
    }
    if (filters.status) {
      const selected = filters.status.split(',').filter(Boolean);
      const statusColumn = table.getColumn('status')
      if (statusColumn) {
        statusColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string[]) => {
          return filterValue.includes(row.original.status)
        }
        statusColumn.setFilterValue(selected)
      }
    }
    if (filters.country) {
      const selected = filters.country.split(',').map(Number).filter(Boolean);
      const countryColumn = table.getColumn('beneficiaryCountry')
      if (countryColumn) {
        countryColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: number[]) => {
          return filterValue.includes(row.original.beneficiaryCountry)
        }
        countryColumn.setFilterValue(selected)
      }
    }
    if (filters.city) {
      const selected = filters.city.split(',').filter(Boolean);
      const cityColumn = table.getColumn('beneficiaryCity')
      if (cityColumn) {
        cityColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string[]) => {
          return filterValue.includes(row.original.beneficiaryCity)
        }
        cityColumn.setFilterValue(selected)
      }
    }
    setHasSearched(true)
    onFilterChanged(filters, 1)
  }

  const handleReset = () => {
    table.resetColumnFilters()
    const f: any = {
      search: '',
      moderatorType: '',
      status: '',
      country: '',
      city: '',
      cityInput: '',
    }
    setFilters(f)
    setHasSearched(false)
    onFilterChanged(f, 0)
  }

  const hasActiveFilters = Boolean(
    (filters.search && filters.search.length > 0) ||
    (filters.moderatorType && filters.moderatorType.length > 0) ||
    (filters.status && filters.status.length > 0) ||
    (filters.country && filters.country.length > 0) ||
    (filters.city && filters.city.length > 0)
  )

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center gap-4 flex-wrap'>
        <Input
          placeholder='Search by name, phone, nickname'
          value={filters.search}
          onChange={(event) => handleFilterChange('search', event.target.value)}
          className='h-9 w-[250px]'
        />
        {/* Multi-select popover for moderator type */}
        <Popover>
          <PopoverTrigger asChild>
            <div style={{ background: "#fff" }} className={cn(
              "flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input px-3 py-2 text-sm shadow-sm cursor-pointer",
              !filters.moderatorType && "text-muted-foreground"
            )}>
              {filters.moderatorType === '' && <span>Select moderator type</span>}
              {filters.moderatorType && filters.moderatorType.split(',').filter(Boolean).slice(0, 2).map((val: string) => (
                <div className="flex gap-1 items-center bg-[#f5f5f5] rounded p-1" key={val}>
                  <Badge className="cursor-default border-0 p-0 bg-transparent text-black">
                    {val}
                  </Badge>
                  <X className="h-3 w-3 cursor-pointer" onClick={e => {
                    e.stopPropagation();
                    handleFilterChange('moderatorType', filters.moderatorType.split(',').filter((v: string) => v !== val).join(','))
                  }} />
                </div>
              ))}
              {filters.moderatorType && filters.moderatorType.split(',').filter(Boolean).length > 2 && (
                <span className="ml-2 text-muted-foreground">...</span>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder="Search moderator type..." />
              <CommandEmpty>No moderator type found.</CommandEmpty>
              <CommandGroup>
                {(() => {
                  const sortedModeratorTypes = [
                    ...moderatorTypeOptions.filter((type) => filters.moderatorType.split(',').includes(type)),
                    ...moderatorTypeOptions.filter((type) => !filters.moderatorType.split(',').includes(type)),
                  ];
                  return sortedModeratorTypes.map((type) => (
                    <CommandItem
                      key={type}
                      onSelect={() => {
                        const selected = filters.moderatorType.split(',').includes(type)
                          ? filters.moderatorType.split(',').filter((v: string) => v !== type).join(',')
                          : [...filters.moderatorType.split(',').filter(Boolean), type].join(',')
                        handleFilterChange('moderatorType', selected)
                      }}
                      className="cursor-pointer"
                    >
                      <span>{type}</span>
                      {filters.moderatorType.split(',').includes(type) && (
                        <span className="ml-auto text-primary">✓</span>
                      )}
                    </CommandItem>
                  ));
                })()}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>
        {/* Multi-select popover for status */}
        <Popover>
          <PopoverTrigger asChild>
            <div style={{ background: "#fff" }} className={cn(
              "flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input px-3 py-2 text-sm shadow-sm cursor-pointer",
              !filters.status && "text-muted-foreground"
            )}>
              {filters.status === '' && <span>Select status</span>}
              {filters.status && filters.status.split(',').filter(Boolean).slice(0, 2).map((val: string) => (
                <div className="flex gap-1 items-center bg-[#f5f5f5] rounded p-1" key={val}>
                  <Badge className="cursor-default border-0 p-0 bg-transparent text-black">
                    {val}
                  </Badge>
                  <X className="h-3 w-3 cursor-pointer" onClick={e => {
                    e.stopPropagation();
                    handleFilterChange('status', filters.status.split(',').filter((v: string) => v !== val).join(','))
                  }} />
                </div>
              ))}
              {filters.status && filters.status.split(',').filter(Boolean).length > 2 && (
                <span className="ml-2 text-muted-foreground">...</span>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder="Search status..." />
              <CommandEmpty>No status found.</CommandEmpty>
              <CommandGroup>
                {(() => {
                  const sortedStatuses = [
                    ...statusOptions.filter((status) => filters.status.split(',').includes(status)),
                    ...statusOptions.filter((status) => !filters.status.split(',').includes(status)),
                  ];
                  return sortedStatuses.map((status) => (
                    <CommandItem
                      key={status}
                      onSelect={() => {
                        const selected = filters.status.split(',').includes(status)
                          ? filters.status.split(',').filter((v: string) => v !== status).join(',')
                          : [...filters.status.split(',').filter(Boolean), status].join(',')
                        handleFilterChange('status', selected)
                      }}
                      className="cursor-pointer"
                    >
                      <span>{status}</span>
                      {filters.status.split(',').includes(status) && (
                        <span className="ml-auto text-primary">✓</span>
                      )}
                    </CommandItem>
                  ));
                })()}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>
        {/* Multi-select popover for countries */}
        <Popover>
          <PopoverTrigger asChild>
            <div style={{ background: "#fff" }} className={cn(
              "flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input  px-3 py-2 text-sm shadow-sm cursor-pointer",
              !filters.country && "text-muted-foreground"
            )}>
              {filters.country === '' && <span>Select countries</span>}
              {filters.country && filters.country.split(',').map((id: string) => {
                const country = countries.find((c: any) => c.id === Number(id));
                return (
                  <div className="flex gap-1 items-center bg-[#f5f5f5] rounded p-1" key={id}>
                    <Badge className="cursor-default border-0 p-0 bg-transparent text-black">
                      {country?.name ?? id}
                    </Badge>
                    <X className="h-3 w-3 cursor-pointer" onClick={e => {
                      e.stopPropagation();
                      handleFilterChange('country', filters.country.split(',').filter((v: string) => v !== id).join(','))
                    }} />
                  </div>
                )
              })}
              {filters.country && filters.country.split(',').length > 2 && (
                <span className="ml-2 text-muted-foreground">...</span>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder="Search countries..." />
              <CommandEmpty>No country found.</CommandEmpty>
              <CommandGroup>
                {(() => {
                  const sortedCountries = [
                    ...countries.filter((c: any) => filters.country.split(',').includes(String(c.id))),
                    ...countries.filter((c: any) => !filters.country.split(',').includes(String(c.id))),
                  ];
                  return sortedCountries.map((country: any) => (
                    <CommandItem
                      key={country.id}
                      onSelect={() => {
                        const selected = filters.country.split(',').includes(String(country.id))
                          ? filters.country.split(',').filter((v: string) => v !== String(country.id)).join(',')
                          : [...filters.country.split(',').filter(Boolean), String(country.id)].join(',')
                        handleFilterChange('country', selected)
                      }}
                      className="cursor-pointer"
                    >
                      <span>{country.name}</span>
                      {filters.country.split(',').includes(String(country.id)) && (
                        <span className="ml-auto text-primary">✓</span>
                      )}
                    </CommandItem>
                  ));
                })()}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>
        {/* Multi-input for cities with dropdown for selected */}
        <div className="relative w-[200px]">
          <input
            ref={cityInputRef}
            type="text"
            style={{ background: '#fff' }}
            className="w-full border border-input rounded-md px-3 py-2 text-sm shadow-sm"
            placeholder={filters.city === '' ? "Type and press Enter to add cities" : "Add city..."}
            value={filters.cityInput || ""}
            onFocus={() => setCityDropdownOpen(true)}
            onBlur={() => setTimeout(() => setCityDropdownOpen(false), 150)}
            onChange={e => handleFilterChange('cityInput', e.target.value)}
            onKeyDown={e => {
              if (
                (e.key === 'Enter' || e.key === ',') &&
                filters.cityInput &&
                !filters.city.split(',').includes(filters.cityInput.trim())
              ) {
                handleFilterChange('city', [...filters.city.split(',').filter(Boolean), filters.cityInput.trim()].join(','));
                handleFilterChange('cityInput', '');
                e.preventDefault();
              }
              if (e.key === 'Backspace' && !filters.cityInput && filters.city) {
                // Remove last city on backspace if input is empty
                handleFilterChange('city', filters.city.split(',').slice(0, -1).join(','));
              }
            }}
          />
          {cityDropdownOpen && filters.city && (
            <div className="absolute left-0 right-0 mt-1 bg-white border border-input rounded-md shadow-lg z-10 p-2 flex flex-wrap gap-1">
              {filters.city.split(',').map((city: string) => (
                <div className="flex gap-1 items-center bg-[#f5f5f5] rounded p-1" key={city}>
                  <Badge className="cursor-default border-0 p-0 bg-transparent text-black">
                    {city}
                  </Badge>
                  <X className="h-3 w-3 cursor-pointer"
                    onMouseDown={e => e.preventDefault()}
                    onClick={() => {
                      handleFilterChange('city', filters.city.split(',').filter((v: string) => v !== city).join(','))
                    }} />
                </div>
              ))}
            </div>
          )}
        </div>
        <Button
          onClick={handleSearch}
          className="h-9 px-3"
          disabled={!hasActiveFilters}
        >
          Search
        </Button>

        {(isFiltered || hasSearched) && (
          <Button
            variant='outline'
            onClick={handleReset}
            className='h-9 px-2 lg:px-3'
          >
            Reset
            {/* <Cross2Icon className='ml-2 h-4 w-4' /> */}
          </Button>
        )}
      </div>
    </div>
  )
}
