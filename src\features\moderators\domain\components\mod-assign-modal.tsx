import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { Command, CommandGroup, CommandItem, CommandInput, CommandEmpty } from "@/components/ui/command";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import { useForm } from "react-hook-form";
import { cn } from "@/lib/utils";

const countries = [
    { label: "United States", value: "usa" },
    { label: "United Kingdom", value: "uk" },
    { label: "Canada", value: "canada" },
    { label: "Australia", value: "australia" },
    { label: "Germany", value: "germany" },
    { label: "France", value: "france" },
    { label: "Spain", value: "spain" },
    { label: "Italy", value: "italy" },
    { label: "Japan", value: "japan" },
    { label: "India", value: "india" },
];

const options1 = [
    { label: "Option 1", value: "option1" },
    { label: "Option 2", value: "option2" },
];
const options2 = [
    { label: "Option A", value: "optionA" },
    { label: "Option B", value: "optionB" },
];

export function ModAssignModal({ open, onClose, onSubmit }: { open: boolean; onClose: () => void; onSubmit?: (values: any) => void }) {
    const form = useForm({
        defaultValues: {
            userId: "",
            domainId: "",
            countriesAssignNative: [],
            countriesAssignHybrid: [],
        },
    });
    const { control, handleSubmit, reset } = form;

    const handleFormSubmit = (values: any) => {
        onSubmit?.(values);
        onClose();
        reset();
    };

    return (
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent className="max-w-lg">
                <DialogHeader>
                    <DialogTitle>Assign WL MOD</DialogTitle>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
                        <FormField
                            name="userId"
                            control={control}
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>User ID</FormLabel>
                                    <FormControl>
                                        <Select value={field.value} onValueChange={field.onChange}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select option" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {options1.map((opt) => (
                                                    <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            name="dropdown2"
                            control={control}
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Domain ID</FormLabel>
                                    <FormControl>
                                        <Select value={field.value} onValueChange={field.onChange}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select option" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {options2.map((opt) => (
                                                    <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={control}
                            name="countriesAssignNative"
                            render={({ field }) => {
                                const selectedValues: string[] = field.value || [];
                                const handleRemove = (value: string) => {
                                    const updated = selectedValues.filter((v) => v !== value);
                                    field.onChange(updated);
                                };
                                const toggleValue = (value: string) => {
                                    const updated = selectedValues.includes(value)
                                        ? selectedValues.filter((v) => v !== value)
                                        : [...selectedValues, value];
                                    field.onChange(updated);
                                };
                                return (
                                    <FormItem>
                                        <FormLabel>Countries Assign as Native</FormLabel>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <FormControl>
                                                    <div
                                                        className={cn(
                                                            "flex min-h-[40px] w-full flex-wrap items-center gap-1 rounded-md border border-input bg-background px-3 py-2 text-sm shadow-sm",
                                                            !selectedValues.length && "text-muted-foreground"
                                                        )}
                                                    >
                                                        {selectedValues.length === 0 && <span>Select countries</span>}
                                                        {selectedValues.map((val) => {
                                                            const label = countries.find((c) => c.value === val)?.label ?? val;
                                                            return (
                                                                <div className="flex gap-1 items-center bg-[#f5f5f5] rounded p-1" key={val}>
                                                                    <Badge className="cursor-default border-0 p-0 bg-transparent text-black">
                                                                        {label}
                                                                    </Badge>
                                                                    <X className="h-3 w-3 cursor-pointer" onClick={(e) => { e.stopPropagation(); handleRemove(val); }} />
                                                                </div>
                                                            );
                                                        })}
                                                    </div>
                                                </FormControl>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-full p-0">
                                                <Command>
                                                    <CommandInput placeholder="Search countries..." />
                                                    <CommandEmpty>No country found.</CommandEmpty>
                                                    <CommandGroup>
                                                        {countries.map((country) => (
                                                            <CommandItem
                                                                key={country.value}
                                                                onSelect={() => toggleValue(country.value)}
                                                                className="cursor-pointer"
                                                            >
                                                                <span>{country.label}</span>
                                                                {selectedValues.includes(country.value) && (
                                                                    <span className="ml-auto text-primary">✓</span>
                                                                )}
                                                            </CommandItem>
                                                        ))}
                                                    </CommandGroup>
                                                </Command>
                                            </PopoverContent>
                                        </Popover>
                                        <FormMessage />
                                    </FormItem>
                                );
                            }}
                        />
                        <FormField
                            control={control}
                            name="countriesAssignHybrid"
                            render={({ field }) => {
                                const selectedValues: string[] = field.value || [];
                                const toggleValue = (value: string) => {
                                    if (selectedValues.includes(value)) {
                                        field.onChange(selectedValues.filter((v) => v !== value));
                                    } else {
                                        field.onChange([...selectedValues, value]);
                                    }
                                };
                                return (
                                    <FormItem>
                                        <FormLabel>Countries Assign as Hybrid</FormLabel>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <FormControl>
                                                    <div
                                                        className={cn(
                                                            "flex min-h-[40px] w-full flex-wrap items-center gap-1 rounded-md border border-input bg-background px-3 py-2 text-sm shadow-sm",
                                                            !selectedValues.length && "text-muted-foreground"
                                                        )}
                                                    >
                                                        {selectedValues.length === 0 && <span>Select countries</span>}
                                                        {selectedValues.map((val) => {
                                                            const label = countries.find((c) => c.value === val)?.label ?? val;
                                                            return (
                                                                <div className="flex gap-1 items-center bg-[#f5f5f5] rounded p-1" key={val}>
                                                                    <Badge className="cursor-default border-0 p-0 bg-transparent text-black">
                                                                        {label}
                                                                    </Badge>
                                                                    <X className="h-3 w-3 cursor-pointer" onClick={(e) => { e.stopPropagation(); toggleValue(val); }} />
                                                                </div>
                                                            );
                                                        })}
                                                    </div>
                                                </FormControl>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-full p-0">
                                                <Command>
                                                    <CommandInput placeholder="Search countries..." />
                                                    <CommandEmpty>No country found.</CommandEmpty>
                                                    <CommandGroup>
                                                        {countries.map((country) => (
                                                            <CommandItem
                                                                key={country.value}
                                                                onSelect={() => toggleValue(country.value)}
                                                                className="cursor-pointer"
                                                            >
                                                                <span>{country.label}</span>
                                                                {selectedValues.includes(country.value) && (
                                                                    <span className="ml-auto text-primary">✓</span>
                                                                )}
                                                            </CommandItem>
                                                        ))}
                                                    </CommandGroup>
                                                </Command>
                                            </PopoverContent>
                                        </Popover>
                                        <FormMessage />
                                    </FormItem>
                                );
                            }}
                        />
                        <DialogFooter>
                            <Button type="submit">Assign</Button>
                            <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
} 