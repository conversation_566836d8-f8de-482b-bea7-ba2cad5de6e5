import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { DashboardTable } from '../dashboard/components/dashboard-table'
import { currenciesData } from './data/sample-data'
import { currenciesColumns } from './components/currencies-table'
import { getCurrencies } from './api'


export default function Currencies() {

    const { data: { currency = [] } = {} } = getCurrencies()

    return (
        <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-base font-medium'>Currencies</CardTitle>
                <div className='flex items-center space-x-2'>
                    {/* <Button variant='ghost' size='sm' className='h-6 w-6 p-0'>
                        <span className='sr-only'>More options</span>
                        <svg className='h-4 w-4' fill='currentColor' viewBox='0 0 20 20'>
                            <path d='M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z' />
                        </svg>
                    </Button> */}
                </div>
            </CardHeader>
            <CardContent>
                <DashboardTable
                    data={currency}
                    columns={currenciesColumns}
                    title='Signups'
                    showToolbar={false}
                    showPagination={false}
                    pageSize={5}
                />
            </CardContent>
        </Card>
    )
}