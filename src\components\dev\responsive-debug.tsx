/**
 * Responsive Debug Component
 * 
 * This component helps developers test and debug responsive behavior.
 * It shows the current breakpoint and window dimensions.
 * 
 * Usage:
 * - Import and add to your layout during development
 * - Remove before production build
 */

import * as React from 'react'
import { useResponsive } from '@/hooks/use-responsive'
import { cn } from '@/lib/utils'

interface ResponsiveDebugProps {
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  show?: boolean
}

export function ResponsiveDebug({ 
  position = 'bottom-right', 
  show = process.env.NODE_ENV === 'development' 
}: ResponsiveDebugProps) {
  const { 
    windowSize, 
    isMobile, 
    isTablet, 
    isDesktop, 
    isSmall, 
    isMedium, 
    isLarge 
  } = useResponsive()

  if (!show) return null

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
  }

  const getCurrentBreakpoint = () => {
    if (isMobile) return 'Mobile'
    if (isTablet) return 'Tablet'
    if (isDesktop) return 'Desktop'
    return 'Unknown'
  }

  const getBreakpointColor = () => {
    if (isMobile) return 'bg-red-500'
    if (isTablet) return 'bg-yellow-500'
    if (isDesktop) return 'bg-green-500'
    return 'bg-gray-500'
  }

  return (
    <div
      className={cn(
        'fixed z-[9999] rounded-lg border bg-background/95 p-3 text-xs font-mono shadow-lg backdrop-blur-sm',
        positionClasses[position]
      )}
    >
      <div className="space-y-1">
        <div className="flex items-center space-x-2">
          <div className={cn('h-2 w-2 rounded-full', getBreakpointColor())} />
          <span className="font-semibold">{getCurrentBreakpoint()}</span>
        </div>
        
        <div className="text-muted-foreground">
          {windowSize.width} × {windowSize.height}
        </div>
        
        <div className="space-y-0.5 text-muted-foreground">
          <div>Mobile: {isMobile ? '✓' : '✗'}</div>
          <div>Tablet: {isTablet ? '✓' : '✗'}</div>
          <div>Desktop: {isDesktop ? '✓' : '✗'}</div>
        </div>
        
        <div className="space-y-0.5 text-muted-foreground">
          <div>Small: {isSmall ? '✓' : '✗'}</div>
          <div>Medium: {isMedium ? '✓' : '✗'}</div>
          <div>Large: {isLarge ? '✓' : '✗'}</div>
        </div>
      </div>
    </div>
  )
}

// Responsive Grid Overlay for visual debugging
export function ResponsiveGridOverlay({ show = false }: { show?: boolean }) {
  if (!show) return null

  return (
    <div className="pointer-events-none fixed inset-0 z-[9998]">
      {/* Mobile Grid */}
      <div className="h-full w-full sm:hidden">
        <div className="grid h-full grid-cols-1 gap-4 p-4">
          {Array.from({ length: 1 }).map((_, i) => (
            <div key={i} className="border border-red-500/20 bg-red-500/5" />
          ))}
        </div>
      </div>
      
      {/* Tablet Grid */}
      <div className="hidden h-full w-full sm:block lg:hidden">
        <div className="grid h-full grid-cols-2 gap-4 p-4">
          {Array.from({ length: 2 }).map((_, i) => (
            <div key={i} className="border border-yellow-500/20 bg-yellow-500/5" />
          ))}
        </div>
      </div>
      
      {/* Desktop Grid */}
      <div className="hidden h-full w-full lg:block">
        <div className="grid h-full grid-cols-3 gap-4 p-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="border border-green-500/20 bg-green-500/5" />
          ))}
        </div>
      </div>
    </div>
  )
}

// Component to test responsive behavior
export function ResponsiveTestCard() {
  const { isMobile, isTablet, isDesktop } = useResponsive()
  
  return (
    <div className="rounded-lg border bg-card p-4 text-card-foreground">
      <h3 className="text-responsive-lg font-semibold">Responsive Test Card</h3>
      <div className="mt-2 space-y-2 text-responsive-sm">
        <p>Current device type:</p>
        <div className="flex space-x-2">
          {isMobile && <span className="rounded bg-red-100 px-2 py-1 text-xs">Mobile</span>}
          {isTablet && <span className="rounded bg-yellow-100 px-2 py-1 text-xs">Tablet</span>}
          {isDesktop && <span className="rounded bg-green-100 px-2 py-1 text-xs">Desktop</span>}
        </div>
        
        <div className="mt-4">
          <p className="text-xs text-muted-foreground">
            This card demonstrates responsive text sizing and spacing.
          </p>
        </div>
      </div>
    </div>
  )
}

// Hook for development-only responsive debugging
export function useResponsiveDebug() {
  const responsive = useResponsive()
  
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Responsive Debug:', {
        windowSize: responsive.windowSize,
        breakpoints: {
          isMobile: responsive.isMobile,
          isTablet: responsive.isTablet,
          isDesktop: responsive.isDesktop,
          isSmall: responsive.isSmall,
          isMedium: responsive.isMedium,
          isLarge: responsive.isLarge,
        }
      })
    }
  }, [responsive])
  
  return responsive
}
