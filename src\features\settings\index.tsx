import { useState } from 'react';
import BasicInformationSetting from './components/BasicInfo';
import PageContentEditor from './page-content';


export default function TabbedView() {
  const [activeTab, setActiveTab] = useState<'basic' | 'page'>('basic');

  return (
    <div className="p-4">
      {/* Top Tabs */}
      <div className="flex border-b mb-4">
        <button
          className={`px-4 py-2 ${activeTab === 'basic' ? 'border-b-2 border-black font-semibold' : 'text-gray-500'
            }`}
          onClick={() => setActiveTab('basic')}
        >
          Basic Informations
        </button>
        <button
          className={`px-4 py-2 ml-4 ${activeTab === 'page' ? 'border-b-2 border-black font-semibold' : 'text-gray-500'
            }`}
          onClick={() => setActiveTab('page')}
        >
          Page Contents
        </button>
      </div>

      {/* Render Component */}
      <div>
        {activeTab === 'basic' ? <BasicInformationSetting /> : <PageContentEditor />}
      </div>
    </div>
  );
}
